@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

.user-account-feature {
  @apply w-full max-w-md mx-auto;
}

.user-profile {
  @apply bg-white rounded-lg shadow-md p-6;
}

.user-profile-header {
  @apply flex items-center space-x-4 mb-6;
}

.user-avatar {
  @apply w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-xl font-semibold;
}

.user-info {
  @apply flex-1;
}

.user-name {
  @apply text-xl font-semibold text-gray-900;
}

.user-email {
  @apply text-gray-600;
}

.login-form {
  @apply bg-white rounded-lg shadow-md p-6 space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-button {
  @apply w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;
}

.form-button:disabled {
  @apply bg-gray-400 cursor-not-allowed;
}

.account-settings {
  @apply bg-white rounded-lg shadow-md p-6 space-y-6;
}

.settings-section {
  @apply border-b border-gray-200 pb-4 last:border-b-0 last:pb-0;
}

.settings-title {
  @apply text-lg font-semibold text-gray-900 mb-3;
}

.error-message {
  @apply text-red-600 text-sm mt-1;
}

.success-message {
  @apply text-green-600 text-sm mt-1;
}