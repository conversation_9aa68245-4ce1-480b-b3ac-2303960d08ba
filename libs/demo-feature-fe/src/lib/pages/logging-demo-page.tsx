/**
 * 统一日志系统演示页面
 * 测试浏览器和服务端统一的logger接口
 */
'use client';

import { logger } from '@yai-investor-insight/shared-fe-core';
import { useState } from 'react';

export function LoggingDemoPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testBrowserLogging = async () => {
    try {
      addResult('🔄 开始测试浏览器统一日志接口...');
      
      // 测试统一的logger接口
      await logger.debug('浏览器统一接口调试日志', {
        component: 'LoggingDemoPage',
        action: 'test_browser_logging',
        feature: 'unified-logging-interface'
      });

      await logger.info('浏览器统一接口信息日志', {
        component: 'LoggingDemoPage',
        action: 'test_browser_logging',
        status: 'success',
        outputs: ['console', 'http-receiver', 'sls']
      });

      await logger.warn('浏览器统一接口警告日志', {
        component: 'LoggingDemoPage',
        action: 'test_browser_logging',
        warning: 'unified interface warning test'
      });

      await logger.error('浏览器统一接口错误日志', {
        component: 'LoggingDemoPage',
        action: 'test_browser_logging',
        error: 'unified interface error test',
        stack: 'simulated error stack for testing'
      });

      addResult('✅ 浏览器统一日志接口测试完成');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addResult(`❌ 浏览器日志测试失败: ${errorMessage}`);
    }
  };

  const testServerLogging = async () => {
    try {
      addResult('🔄 开始测试服务端统一日志接口...');
      
      // 直接调用服务端日志函数进行测试
      const { logger } = await import('@yai-investor-insight/shared-fe-core/server');
      
      await logger.debug('demo服务端调试日志', {
        component: 'LoggingDemoPage',
        testType: 'unified-interface',
        level: 'debug'
      });

      await logger.info('demo服务端信息日志', {
        component: 'LoggingDemoPage',
        testType: 'unified-interface',
        action: 'direct_call',
        status: 'success'
      });

      await logger.warn('demo服务端警告日志', {
        component: 'LoggingDemoPage',
        testType: 'unified-interface',
        warning: 'direct call warning test'
      });

      await logger.error('demo服务端错误日志', {
        component: 'LoggingDemoPage',
        testType: 'unified-interface',
        error: 'direct call error test',
        stack: 'simulated error stack'
      });
      
      addResult(`✅ 服务端统一日志接口测试完成: 4 条日志已写入`);
      addResult(`📁 服务端日志文件: logs/webapp.server.log`);
      addResult(`🔧 输出配置: {"console":${process.env.NODE_ENV === 'development'},"file":true,"sls":${process.env.NEXT_PUBLIC_SLS_ENABLED === 'true'}}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addResult(`❌ 服务端日志测试失败: ${errorMessage}`);
    }
  };

  const testBothLoggers = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    try {
      addResult('🚀 开始测试统一日志系统...');
      
      // 并行测试浏览器和服务端日志
      await Promise.all([
        testBrowserLogging(),
        testServerLogging()
      ]);
      
      addResult('🎉 统一日志系统测试完成！');
      addResult('📋 请检查以下文件：');
      addResult('   - logs/webapp.client.log (浏览器日志)');
      addResult('   - logs/webapp.server.log (服务端日志)');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addResult(`❌ 统一日志系统测试失败: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">统一日志系统演示</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">🎯 功能说明</h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">浏览器统一接口：</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>logger.info/debug/warn/error 直接调用</li>
              <li>自动输出到控制台</li>
              <li>自动发送到 /api/logs/client 写入文件</li>
              <li>配置开启时自动上报SLS</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-2">服务端统一接口：</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>logger.info/debug/warn/error 直接调用</li>
              <li>自动输出到控制台（开发环境）</li>
              <li>自动记录到 webapp.server.log</li>
              <li>配置开启时自动上报SLS</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-4 mb-6">
        <button
          onClick={testBrowserLogging}
          disabled={isLoading}
          className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors"
        >
          🌐 测试浏览器日志
        </button>
        
        <button
          onClick={testServerLogging}
          disabled={isLoading}
          className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors"
        >
          🖥️ 测试服务端日志
        </button>
        
        <button
          onClick={testBothLoggers}
          disabled={isLoading}
          className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors"
        >
          {isLoading ? '🔄 测试中...' : '🚀 测试统一系统'}
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold mb-3">📋 测试结果：</h3>
          <div className="bg-black text-green-400 p-3 rounded font-mono text-sm max-h-80 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="mb-1">{result}</div>
            ))}
          </div>
        </div>
      )}

      <div className="grid md:grid-cols-2 gap-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-3">✅ 实现的统一功能</h3>
          <ul className="list-disc list-inside space-y-1 text-sm text-green-700">
            <li>业务代码统一调用 logger.info/error</li>
            <li>配置驱动的多输出支持</li>
            <li>浏览器+服务端一致的接口</li>
            <li>自动批量处理和重试机制</li>
            <li>环境检测和回退机制</li>
            <li>SLS集成支持</li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-3">⚙️ 配置说明</h3>
          <ul className="list-disc list-inside space-y-1 text-sm text-yellow-700">
            <li>NODE_ENV=development: 启用控制台输出</li>
            <li>NEXT_PUBLIC_SLS_ENABLED=true: 启用SLS上报</li>
            <li>NEXT_PUBLIC_LOG_RECEIVER_ENABLED=true: 启用文件输出</li>
            <li>K8s环境自动优化批量大小和间隔</li>
            <li>SLS配置不完整时自动回退到控制台</li>
          </ul>
        </div>
      </div>
    </div>
  );
}