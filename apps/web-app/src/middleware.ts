import { NextRequest, NextResponse } from 'next/server';
import { getIronSession } from 'iron-session';
import { getDefaultSessionOptions, SessionData } from '@yai-investor-insight/shared-fe-core';

// 需要认证的路由
const protectedRoutes = [
  '/tasks',
  '/research-v2',
  '/profile',
  '/workbench-v1a'
];

// 公开路由（不需要认证）
const publicRoutes = [
  '/',
  '/login',
  '/register',
  '/api',
  '/_next',
  '/favicon.ico'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是公开路由
  const isPublicRoute = publicRoutes.some(route => 
    pathname.startsWith(route)
  );

  // 如果是公开路由，直接通过
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // 检查是否是受保护的路由
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );

  if (isProtectedRoute) {
    try {
      // 创建响应对象用于session
      const response = NextResponse.next();
      
      // 获取session
      const sessionOptions = await getDefaultSessionOptions();
      const session = await getIronSession<SessionData>(
        request,
        response,
        sessionOptions
      );

      // 检查用户是否已登录
      if (!session.isLoggedIn) {
        // 重定向到登录页，并保存原始URL
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('redirect', pathname);
        return NextResponse.redirect(loginUrl);
      }

      // 用户已登录，继续访问
      return response;
    } catch (error) {
      console.error('Middleware session error:', error);
      // 如果session解析失败，重定向到登录页
      const loginUrl = new URL('/login', request.url);
      return NextResponse.redirect(loginUrl);
    }
  }

  // 其他路由直接通过
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};