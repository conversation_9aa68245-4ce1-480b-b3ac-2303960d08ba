'use client';

import { useState } from 'react';
import { 
  ChartBarIcon, 
  DocumentTextIcon, 
  CogIcon,
  PlusIcon,
  FolderIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface WorkspaceItem {
  id: string;
  name: string;
  type: 'analysis' | 'report' | 'research';
  status: 'active' | 'completed' | 'draft';
  lastModified: string;
  description: string;
}

const mockWorkspaceItems: WorkspaceItem[] = [
  {
    id: '1',
    name: '腾讯控股深度分析',
    type: 'analysis',
    status: 'active',
    lastModified: '2小时前',
    description: '基于Q3财报的综合投资分析'
  },
  {
    id: '2', 
    name: '新能源板块研究报告',
    type: 'report',
    status: 'completed',
    lastModified: '1天前',
    description: '2024年新能源汽车行业趋势分析'
  },
  {
    id: '3',
    name: '美股科技股调研',
    type: 'research', 
    status: 'draft',
    lastModified: '3天前',
    description: 'FAANG公司最新业务发展调研'
  }
];

export function WorkbenchV1APage() {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'projects' | 'tools'>('overview');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'analysis': return <ChartBarIcon className="h-5 w-5" />;
      case 'report': return <DocumentTextIcon className="h-5 w-5" />;
      case 'research': return <FolderIcon className="h-5 w-5" />;
      default: return <DocumentTextIcon className="h-5 w-5" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          工作台 V1-A (Workbench)
        </h1>
        <p className="text-gray-600">
          智能投资分析工作台，集成研究工具、数据分析和报告生成功能
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { key: 'overview', label: '概览', icon: ChartBarIcon },
            { key: 'projects', label: '项目管理', icon: FolderIcon },
            { key: 'tools', label: '分析工具', icon: CogIcon }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setSelectedTab(key as 'overview' | 'projects' | 'tools')}
              className={`flex items-center space-x-2 px-3 py-2 border-b-2 font-medium text-sm transition-colors ${
                selectedTab === key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* 内容区域 */}
      <div className="space-y-6">
        {selectedTab === 'overview' && (
          <>
            {/* 快速操作 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">快速操作</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button className="flex items-center justify-center space-x-2 h-12">
                  <PlusIcon className="h-5 w-5" />
                  <span>新建分析项目</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2 h-12">
                  <DocumentTextIcon className="h-5 w-5" />
                  <span>导入数据</span>
                </Button>
                <Button variant="outline" className="flex items-center justify-center space-x-2 h-12">
                  <ChartBarIcon className="h-5 w-5" />
                  <span>查看报告</span>
                </Button>
              </div>
            </div>

            {/* 最近项目 */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">最近项目</h2>
                <Button variant="ghost" size="sm">查看全部</Button>
              </div>
              <div className="space-y-4">
                {mockWorkspaceItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="text-gray-400">
                        {getTypeIcon(item.type)}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{item.name}</h3>
                        <p className="text-sm text-gray-500">{item.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge className={getStatusColor(item.status)}>
                        {item.status === 'active' ? '进行中' : 
                         item.status === 'completed' ? '已完成' : '草稿'}
                      </Badge>
                      <div className="flex items-center text-sm text-gray-500">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        {item.lastModified}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {selectedTab === 'projects' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">项目管理</h2>
            <p className="text-gray-600 mb-6">管理您的投资分析项目，跟踪进度和协作</p>
            <div className="text-center py-12">
              <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">项目管理功能正在开发中...</p>
            </div>
          </div>
        )}

        {selectedTab === 'tools' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">分析工具</h2>
            <p className="text-gray-600 mb-6">使用专业的投资分析工具进行深度研究</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { name: '财务分析', description: '深度财务数据分析工具', icon: ChartBarIcon },
                { name: '市场研究', description: '行业和市场趋势分析', icon: DocumentTextIcon },
                { name: '风险评估', description: '投资风险量化分析', icon: CogIcon }
              ].map((tool, index) => (
                <div key={index} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <tool.icon className="h-8 w-8 text-blue-600 mb-3" />
                  <h3 className="font-medium text-gray-900 mb-2">{tool.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{tool.description}</p>
                  <Button size="sm" variant="outline">启动工具</Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
