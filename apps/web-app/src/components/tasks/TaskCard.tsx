'use client';

import { AnalysisTask, TaskStatus } from '@yai-investor-insight/shared-types';
import { <PERSON>, Card<PERSON><PERSON>er, CardContent, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { 
  ClockIcon, 
  PlayIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  EyeIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useDeleteTask } from '@/hooks/useTasks';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface TaskCardProps {
  task: AnalysisTask;
  onView?: (task: AnalysisTask) => void;
}

const statusConfig = {
  [TaskStatus.PENDING]: {
    icon: ClockIcon,
    variant: 'warning' as const,
    label: '等待中'
  },
  [TaskStatus.PROCESSING]: {
    icon: PlayIcon,
    variant: 'info' as const,
    label: '处理中'
  },
  [TaskStatus.COMPLETED]: {
    icon: CheckCircleIcon,
    variant: 'success' as const,
    label: '已完成'
  },
  [TaskStatus.FAILED]: {
    icon: XCircleIcon,
    variant: 'danger' as const,
    label: '失败'
  }
};

const priorityConfig = {
  low: { variant: 'default' as const, label: '低' },
  normal: { variant: 'info' as const, label: '普通' },
  high: { variant: 'warning' as const, label: '高' },
  urgent: { variant: 'danger' as const, label: '紧急' }
};

const analysisTypeLabels = {
  stock_analysis: '股票分析',
  market_trend: '市场趋势',
  financial_report: '财务报告',
  news_sentiment: '新闻情感'
};

export function TaskCard({ task, onView }: TaskCardProps) {
  const deleteTaskMutation = useDeleteTask();
  
  const statusInfo = statusConfig[task.status];
  const StatusIcon = statusInfo.icon;
  const priorityInfo = priorityConfig[task.priority];

  const handleDelete = () => {
    if (confirm('确定要删除这个任务吗？')) {
      deleteTaskMutation.mutate(task.id);
    }
  };

  const handleView = () => {
    onView?.(task);
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {task.title}
            </h3>
            <div className="flex items-center space-x-2 mb-2">
              <Badge variant={statusInfo.variant} size="sm">
                <StatusIcon className="w-3 h-3 mr-1" />
                {statusInfo.label}
              </Badge>
              <Badge variant={priorityInfo.variant} size="sm">
                {priorityInfo.label}优先级
              </Badge>
              <Badge variant="default" size="sm">
                {analysisTypeLabels[task.analysis_type] || task.analysis_type}
              </Badge>
            </div>
          </div>
        </div>
        
        {task.description && (
          <p className="text-sm text-gray-600 line-clamp-2">
            {task.description}
          </p>
        )}
      </CardHeader>

      <CardContent>
        <div className="space-y-2 text-sm text-gray-500">
          <div className="flex justify-between">
            <span>创建时间:</span>
            <span>
              {formatDistanceToNow(new Date(task.created_at), { 
                addSuffix: true, 
                locale: zhCN 
              })}
            </span>
          </div>
          
          {task.started_at && (
            <div className="flex justify-between">
              <span>开始时间:</span>
              <span>
                {formatDistanceToNow(new Date(task.started_at), { 
                  addSuffix: true, 
                  locale: zhCN 
                })}
              </span>
            </div>
          )}
          
          {task.completed_at && (
            <div className="flex justify-between">
              <span>完成时间:</span>
              <span>
                {formatDistanceToNow(new Date(task.completed_at), { 
                  addSuffix: true, 
                  locale: zhCN 
                })}
              </span>
            </div>
          )}

          {task.retry_count > 0 && (
            <div className="flex justify-between">
              <span>重试次数:</span>
              <span>{task.retry_count}/{task.max_retries}</span>
            </div>
          )}

          {task.ai_tools_used && task.ai_tools_used.length > 0 && (
            <div className="flex justify-between">
              <span>使用工具:</span>
              <span>{task.ai_tools_used.join(', ')}</span>
            </div>
          )}
        </div>

        {/* 处理步骤进度 */}
        {task.processing_steps && task.processing_steps.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              处理进度
            </h4>
            <div className="space-y-1">
              {task.processing_steps.map((step, index) => (
                <div key={index} className="flex items-center space-x-2 text-xs">
                  <div className={`w-2 h-2 rounded-full ${
                    step.status === 'completed' ? 'bg-green-500' :
                    step.status === 'processing' ? 'bg-blue-500' :
                    step.status === 'failed' ? 'bg-red-500' :
                    'bg-gray-300'
                  }`} />
                  <span className="text-gray-600">
                    {step.step_name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter>
        <div className="flex justify-between items-center w-full">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleView}
            >
              <EyeIcon className="w-4 h-4 mr-1" />
              查看
            </Button>
            
            {task.status === TaskStatus.FAILED && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {/* TODO: 重试逻辑 */}}
              >
                <PlayIcon className="w-4 h-4 mr-1" />
                重试
              </Button>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            disabled={deleteTaskMutation.isPending}
          >
            <TrashIcon className="w-4 h-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
