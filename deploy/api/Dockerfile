# Dockerfile for API Server
FROM beiyikeji-registry.cn-beijing.cr.aliyuncs.com/common/python-base-image:3.12-slim-tools

# 使用构建参数来指定应用程序的路径
ARG APP_PATH=apps/api-server

# 设置工作目录为项目根目录，保持与本地开发环境一致的相对路径结构
WORKDIR /app

# 设置环境变量
# 使用系统 Python，这在 Docker 容器中是推荐的做法
ENV UV_SYSTEM_PYTHON=1
# Docker环境标识，用于构建脚本判断运行环境
ENV DOCKER_BUILD=1

# 复制构建脚本和项目结构
COPY scripts/ /app/scripts/
COPY libs/ /app/libs/
COPY ${APP_PATH}/ /app/apps/api-server/

# Docker环境下的特殊处理：修改pyproject.toml中的workspace依赖为本地路径
RUN cd /app/apps/api-server && \
    cp pyproject.toml pyproject.toml.backup && \
    sed -i -e 's|demo-feature-bs = { workspace = true }|demo-feature-bs = { path = "/app/libs/demo-feature-bs" }|' \
           -e 's|shared-bs-llm = { workspace = true }|shared-bs-llm = { path = "/app/libs/shared-bs-llm" }|' \
           -e 's|research-v2-bs = { workspace = true }|research-v2-bs = { path = "/app/libs/research-v2-bs" }|' \
           -e 's|research-v2b-bs = { workspace = true }|research-v2b-bs = { path = "/app/libs/research-v2b-bs" }|' \
           -e 's|shared-bs-core = { workspace = true }|shared-bs-core = { path = "/app/libs/shared-bs-core" }|' \
           -e 's|research-v2h-bs = { workspace = true }|research-v2h-bs = { path = "/app/libs/research-v2h-bs" }|' pyproject.toml

# 给构建脚本执行权限并运行构建
RUN chmod +x /app/scripts/build-backend.sh && \
    /app/scripts/build-backend.sh

# 暴露端口
EXPOSE 8000

# 设置工作目录为应用目录
WORKDIR /app/apps/api-server

# 启动命令 - 使用 uvicorn 启动 FastAPI 应用
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]